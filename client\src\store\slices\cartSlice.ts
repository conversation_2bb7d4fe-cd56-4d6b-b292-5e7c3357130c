import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import * as cartApi from '@/services/cartApi';

export interface CartItem {
  id: string;
  userId: string;
  userType: 'STUDENT' | 'CLASS';
  itemId: string;
  quantity: number;
  createdAt: string;
  updatedAt: string;
  item: {
    id: string;
    name: string;
    coinPrice: number;
    image: string | null;
    availableStock: number;
  };
}

export interface CartState {
  items: CartItem[];
  totalCoins: number;
  totalItems: number;
  itemCount: number;
  loading: boolean;
  error: string | null;
  lastUpdated: number | null;
}

const initialState: CartState = {
  items: [],
  totalCoins: 0,
  totalItems: 0,
  itemCount: 0,
  loading: false,
  error: null,
  lastUpdated: null,
};

// Async thunks
export const fetchCartItems = createAsyncThunk(
  'cart/fetchCartItems',
  async (_, { rejectWithValue }) => {
    try {
      const result = await cartApi.getCartItems();
      if (!result.success) {
        return rejectWithValue(result.error || 'Failed to fetch cart items');
      }
      return result.data || [];
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch cart items');
    }
  }
);

export const addToCartAsync = createAsyncThunk(
  'cart/addToCart',
  async ({ itemId, quantity = 1 }: { itemId: string; quantity?: number }, { rejectWithValue }) => {
    try {
      const result = await cartApi.addToCart(itemId, quantity);
      if (!result.success) {
        return rejectWithValue(result.error || 'Failed to add item to cart');
      }
      return result.data;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to add item to cart');
    }
  }
);

export const updateCartItemQuantityAsync = createAsyncThunk(
  'cart/updateQuantity',
  async ({ itemId, quantity }: { itemId: string; quantity: number }, { rejectWithValue }) => {
    try {
      if (quantity <= 0) {
        const result = await cartApi.removeFromCart(itemId);
        if (!result.success) {
          return rejectWithValue(result.error || 'Failed to remove item from cart');
        }
        return { itemId, removed: true };
      } else {
        const result = await cartApi.updateCartItemQuantity(itemId, quantity);
        if (!result.success) {
          return rejectWithValue(result.error || 'Failed to update cart item');
        }
        return result.data;
      }
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to update cart item');
    }
  }
);

export const removeFromCartAsync = createAsyncThunk(
  'cart/removeFromCart',
  async (itemId: string, { rejectWithValue }) => {
    try {
      const result = await cartApi.removeFromCart(itemId);
      if (!result.success) {
        return rejectWithValue(result.error || 'Failed to remove item from cart');
      }
      return itemId;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to remove item from cart');
    }
  }
);

export const clearCartAsync = createAsyncThunk(
  'cart/clearCart',
  async (_, { rejectWithValue }) => {
    try {
      const result = await cartApi.clearCart();
      if (!result.success) {
        return rejectWithValue(result.error || 'Failed to clear cart');
      }
      return true;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to clear cart');
    }
  }
);

const cartSlice = createSlice({
  name: 'cart',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateCartTotals: (state) => {
      state.totalItems = state.items.reduce((total, item) => total + item.quantity, 0);
      state.totalCoins = state.items.reduce((total, item) => total + (item.item.coinPrice * item.quantity), 0);
      state.itemCount = state.items.length;
      state.lastUpdated = Date.now();
    },
    resetCart: (state) => {
      state.items = [];
      state.totalCoins = 0;
      state.totalItems = 0;
      state.itemCount = 0;
      state.error = null;
      state.lastUpdated = Date.now();
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch cart items
      .addCase(fetchCartItems.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCartItems.fulfilled, (state, action) => {
        state.loading = false;
        state.items = action.payload;
        cartSlice.caseReducers.updateCartTotals(state);
      })
      .addCase(fetchCartItems.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Add to cart
      .addCase(addToCartAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(addToCartAsync.fulfilled, (state, action) => {
        state.loading = false;
        const newItem = action.payload;
        const existingItemIndex = state.items.findIndex(item => item.itemId === newItem.itemId);
        
        if (existingItemIndex >= 0) {
          state.items[existingItemIndex] = newItem;
        } else {
          state.items.push(newItem);
        }
        cartSlice.caseReducers.updateCartTotals(state);
      })
      .addCase(addToCartAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Update quantity
      .addCase(updateCartItemQuantityAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updateCartItemQuantityAsync.fulfilled, (state, action) => {
        state.loading = false;
        const payload = action.payload;
        
        if ('removed' in payload && payload.removed) {
          state.items = state.items.filter(item => item.itemId !== payload.itemId);
        } else {
          const updatedItem = payload as CartItem;
          const existingItemIndex = state.items.findIndex(item => item.itemId === updatedItem.itemId);
          if (existingItemIndex >= 0) {
            state.items[existingItemIndex] = updatedItem;
          }
        }
        cartSlice.caseReducers.updateCartTotals(state);
      })
      .addCase(updateCartItemQuantityAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Remove from cart
      .addCase(removeFromCartAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(removeFromCartAsync.fulfilled, (state, action) => {
        state.loading = false;
        state.items = state.items.filter(item => item.itemId !== action.payload);
        cartSlice.caseReducers.updateCartTotals(state);
      })
      .addCase(removeFromCartAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Clear cart
      .addCase(clearCartAsync.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(clearCartAsync.fulfilled, (state) => {
        state.loading = false;
        cartSlice.caseReducers.resetCart(state);
      })
      .addCase(clearCartAsync.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { clearError, updateCartTotals, resetCart } = cartSlice.actions;
export default cartSlice.reducer;
